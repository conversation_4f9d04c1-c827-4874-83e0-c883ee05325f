# Azure Functions Authentication System Application Separation Analysis

**Date:** August 14th, 2024  
**System:** PowerPagesCustomAuth Azure Functions  
**Scope:** Multi-Application Support within Single Entra External ID Tenant

## Executive Summary

This analysis examines the Azure Functions authentication system's capability to serve multiple Power Pages sites within the same Entra External ID tenant while maintaining proper user account segmentation. The system demonstrates **strong application isolation mechanisms** with some **moderate security risks** that require attention for production deployment.

**Overall Assessment:** ✅ **SUITABLE FOR MULTI-APPLICATION USE** with recommended security enhancements.

---

## 1. User Account Segmentation Analysis

### Current Implementation ✅ **STRONG**

The system implements robust user account isolation through:

#### **Department Field Strategy**
- **Mechanism**: Uses Entra ID `Department` field to store application context
- **Implementation**: All user queries filter by `(email AND department eq 'ApplicationName')`
- **Isolation Level**: Complete separation - same email can exist in multiple applications

```csharp
// Example from AuthenticationFunction.cs
var filter = $"(mail eq '{emailEsc}' or userPrincipalName eq '{emailEsc}' or proxyAddresses/any(c:c eq 'SMTP:{emailEsc}')) and department eq '{appEsc}'";
```

#### **User Creation Process**
- **Display Name**: Includes application context: `"John Doe (Customer Portal)"`
- **Department Field**: Set to `ApplicationName` parameter
- **Unique UPN**: Generated per user to avoid conflicts

### Security Assessment

**✅ Strengths:**
- Consistent filtering across all functions (Authentication, Password, Registration)
- Same email address can register separately for different applications
- No cross-application data leakage in user queries

**⚠️ Risks:**
- **Application Name Spoofing**: Client-controlled parameter (MEDIUM RISK)
- **Non-Standard Field Usage**: Department field not intended for application context (LOW RISK)

---

## 2. Password History Isolation

### Current Implementation ✅ **EXCELLENT**

The password history system demonstrates **exemplary application separation**:

#### **Scoped User ID Strategy**
```csharp
// From PasswordHistoryService.cs
private string GetScopedUserId(string userId, string? applicationId)
{
    if (string.IsNullOrEmpty(applicationId))
        return userId;
    
    var sanitizedAppId = applicationId
        .Replace("/", "").Replace("\\", "").Replace("..", "")
        .Replace(":", "").Trim();
    
    return $"{sanitizedAppId}/{userId}";
}
```

#### **Storage Isolation**
- **Blob Storage**: Password histories stored with application-prefixed keys
- **Cache Keys**: Include application context: `password_history_{scopedUserId}`
- **Complete Separation**: No possibility of cross-application password history access

### Security Assessment

**✅ Strengths:**
- Perfect application isolation in password history storage
- Sanitized application IDs prevent path traversal attacks
- Cached data properly scoped per application

**✅ No Identified Risks** in password history isolation.

---

## 3. Authentication Flow Separation

### Current Implementation ✅ **STRONG**

All authentication flows maintain application-specific context:

#### **Token Management Systems**

**Reset Token Manager:**
```csharp
// Application ID stored in token data
var tokenData = new ResetTokenData
{
    Email = email,
    ApplicationId = applicationId,  // ✅ Application-specific
    Token = token,
    VerificationCode = verificationCode,
    // ...
};
```

**Invitation Token Manager:**
```csharp
// Invalidation scoped to application
if (tokenData.Email.Equals(email, StringComparison.OrdinalIgnoreCase) &&
    tokenData.ApplicationId.Equals(applicationId, StringComparison.OrdinalIgnoreCase))
{
    // Only invalidate tokens for same email + application
}
```

#### **Verification Code Isolation**
- **6-digit codes**: Generated per application context
- **Validation**: Requires both verification code AND application match
- **Expiration**: Properly scoped to application boundaries

### Security Assessment

**✅ Strengths:**
- All tokens include application context
- Verification codes cannot be used across applications
- Token invalidation properly scoped

**⚠️ Risks:**
- **Client-Controlled Application Parameter**: Same spoofing risk as user management (MEDIUM RISK)

---

## 4. Integration Requirements per Power Pages Application

### Azure Function App Configuration

**Required Settings (Shared across all applications):**
```json
{
  "EntraExternalID:TenantId": "your-tenant-id",
  "EntraExternalID:ClientId": "your-client-id",
  "EntraExternalID:ClientSecret": "@Microsoft.KeyVault(...)",
  "SendGrid:ApiKey": "@Microsoft.KeyVault(...)",
  "AzureWebJobsStorage": "connection-string"
}
```

### Power Pages Site Configuration

**Required Settings (Per Application):**
```
AzureFunctionUrl = https://your-function-app.azurewebsites.net/api
ApplicationName = [Unique Application Name]  ⚠️ CRITICAL
MSALClientId = [same-client-id-as-function]
MSALTenantId = [same-tenant-id-as-function]
AzureFunctionKey = [function-key-for-security]
```

### Authentication Provider Configuration

**Power Pages Authentication Settings:**
```
Authentication/Registration/Enabled = false
Authentication/Registration/RequiresConfirmation = false
Authentication/Registration/RequiresInvitation = false
Authentication/OpenIdConnect/[Provider]/Authority = https://login.microsoftonline.com/[tenant-id]/v2.0
```

### Integration Steps for New Applications

1. **Create Power Pages Site**
2. **Configure Site Settings** (ApplicationName must be unique)
3. **Set Authentication Provider** (openid_2 pattern)
4. **Deploy Power Pages Files** (registration.html, forgot-password.html, etc.)
5. **Test Application Isolation** (verify users cannot cross applications)

---

## 5. Current Implementation Assessment

### Identified Security Gaps

#### **HIGH PRIORITY**

**1. CORS Configuration** 🚨
- **Current**: Allows all origins (`*`)
- **Risk**: Any website can call Azure Functions
- **Fix Required**: Restrict to specific Power Pages domains

**2. Function Authorization Level** ⚠️
- **Current**: Some functions may use `AuthorizationLevel.Anonymous`
- **Risk**: Unauthenticated access to sensitive operations
- **Fix Required**: Change to `AuthorizationLevel.Function`

#### **MEDIUM PRIORITY**

**3. Application Name Validation** ⚠️
- **Current**: Client-controlled parameter without server-side validation
- **Risk**: Application spoofing attacks
- **Recommended Fix**: Server-side whitelist validation

```csharp
private static readonly HashSet<string> ValidApplications = new()
{
    "Customer Portal", "Employee Portal", "Partner Portal"
};
```

**4. Origin Validation** ⚠️
- **Current**: No request source verification
- **Risk**: Cross-site request forgery
- **Recommended Fix**: Validate request origin headers

### Architecture Strengths

**✅ Excellent Application Separation:**
- Password history completely isolated
- Token management application-aware
- User queries properly filtered
- Configuration properly scoped

**✅ Scalable Design:**
- Single Azure Function App serves multiple applications
- Minimal configuration required per new application
- Consistent authentication patterns

**✅ Security Foundation:**
- BCrypt password hashing with configurable work factor
- Secure token generation using cryptographic RNG
- Azure Key Vault integration for sensitive configuration

---

## Recommendations

### Immediate Actions (Before Production)

1. **Configure CORS** - Restrict to specific Power Pages domains
2. **Enable Function Authorization** - Require function keys
3. **Implement Application Whitelist** - Server-side validation
4. **Security Testing** - Verify application isolation

### Long-term Enhancements

1. **Custom Attributes** - Move from Department field to extension attributes
2. **Enhanced Monitoring** - Application-specific telemetry
3. **Rate Limiting** - Per-application rate limits
4. **Audit Logging** - Cross-application access attempts

---

## Conclusion

The Azure Functions authentication system demonstrates **strong application separation capabilities** suitable for serving multiple Power Pages sites. The password history isolation is particularly well-implemented, and the token management systems properly maintain application context.

**Key Success Factors:**
- Consistent application filtering across all operations
- Robust password history isolation using scoped user IDs
- Application-aware token management systems

**Security Posture:** With the recommended CORS and authorization fixes, this system provides **enterprise-grade application separation** suitable for production use in a multi-application environment.

**Deployment Readiness:** ✅ **READY** with security enhancements applied.
