using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using System.Net;
using System.Text.Json;
using PasswordHistoryValidator.Services;
using PasswordHistoryValidator.Shared;
using System.Linq;

namespace PasswordHistoryValidator;

public class AuthenticationFunction : BaseFunctionService
{
    private readonly ILogger<AuthenticationFunction> _logger;
    private readonly IPasswordHistoryService _passwordHistoryService;
    private readonly GraphServiceClient _graphServiceClient;

    public AuthenticationFunction(
        ILogger<AuthenticationFunction> logger,
        IPasswordHistoryService passwordHistoryService,
        GraphServiceClient graphServiceClient,
        JsonSerializerOptions jsonOptions) : base(jsonOptions)
    {
        _logger = logger;
        _passwordHistoryService = passwordHistoryService;
        _graphServiceClient = graphServiceClient;
    }


    /// Authentication service for credential validation

    [Function("AuthenticationService")]
    public async Task<HttpResponseData> Run(
        [HttpTrigger(AuthorizationLevel.Function, "post", "options")] HttpRequestData req,
        CancellationToken cancellationToken)
    {
        var correlationId = GenerateCorrelationId();

        if (req.Method.Equals("OPTIONS", StringComparison.OrdinalIgnoreCase))
        {
            return CreateCorsResponse(req);
        }

        try
        {
            var operation = req.Query["operation"];
            if (string.IsNullOrEmpty(operation))
            {
                return await CreateErrorResponse(req, "Operation parameter required", correlationId);
            }

            return operation.ToLower() switch
            {
                "login" => await HandleUserLogin(req, correlationId, cancellationToken),
                "validate-credentials" => await HandleCredentialValidation(req, correlationId, cancellationToken),
                _ => await CreateErrorResponse(req, $"Invalid operation: {operation}", correlationId)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Auth service error [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Service error", correlationId);
        }
    }

    private async Task<HttpResponseData> HandleUserLogin(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        var data = await JsonSerializer.DeserializeAsync<AuthRequest>(req.Body, JsonOptions, cancellationToken);
        if (data == null || string.IsNullOrWhiteSpace(data.Email) || string.IsNullOrWhiteSpace(data.Password))
            return await CreateErrorResponse(req, "Email and password required", correlationId);

        var applicationName = data.ApplicationName ?? "Default Application";

        // Find user in Entra External ID by email and application context
        var emailEsc = ODataHelpers.EscapeString(data.Email);
        var appEsc = ODataHelpers.EscapeString(applicationName);
        var users = await _graphServiceClient.Users
            .GetAsync(requestConfiguration =>
            {
                requestConfiguration.QueryParameters.Filter =
                    $"(mail eq '{emailEsc}' or userPrincipalName eq '{emailEsc}' or proxyAddresses/any(c:c eq 'SMTP:{emailEsc}')) and department eq '{appEsc}'";
                requestConfiguration.QueryParameters.Select = new[] { "id", "userPrincipalName", "identities", "mail", "displayName", "department" };
                requestConfiguration.QueryParameters.Top = 1;
                requestConfiguration.QueryParameters.Orderby = new[] { "createdDateTime desc" };
            }, cancellationToken);

        if (users?.Value == null || users.Value.Count == 0)
        {
            _logger.LogWarning("Login failed for {Email} [CorrelationId: {CorrelationId}]", data.Email, correlationId);
            return await CreateErrorResponse(req, "Invalid email or password", correlationId);
        }

        // Use first user (query already ordered by createdDateTime desc)
        var user = users.Value.First();
        if (users.Value.Count > 1)
        {
            _logger.LogWarning("Multiple users found for {Email} in application {ApplicationName} [CorrelationId: {CorrelationId}]",
                data.Email, applicationName, correlationId);
        }

        // Validate credentials using password history
        var isValidCredentials = await ValidateUserCredentials(data.Password, applicationName, data.Email, cancellationToken);

        if (!isValidCredentials)
            return await CreateErrorResponse(req, "Invalid email or password", correlationId);

        return await CreateJsonResponse(req, new
        {
            success = true,
            message = "Login successful"
        }, HttpStatusCode.OK, correlationId);
    }

    private async Task<bool> ValidateUserCredentials(string password, string applicationName, string email, CancellationToken cancellationToken)
    {
        var historyResult = await _passwordHistoryService.GetPasswordHistoryAsync(
            applicationName ?? "Default Application",
            email,
            cancellationToken);

        if (historyResult.IsSuccess && historyResult.Value != null && historyResult.Value.Count > 0)
        {
            var mostRecentHash = historyResult.Value.First();
            return BCrypt.Net.BCrypt.Verify(password, mostRecentHash);
        }

        return false;
    }



    private async Task<HttpResponseData> HandleCredentialValidation(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        var data = await JsonSerializer.DeserializeAsync<AuthRequest>(req.Body, JsonOptions, cancellationToken);
        if (data == null || string.IsNullOrWhiteSpace(data.Email) || string.IsNullOrWhiteSpace(data.Password))
            return await CreateErrorResponse(req, "Email and password required", correlationId);

        var applicationName = data.ApplicationName ?? "Default Application";

        // Find user by email and application context
        var emailEsc = ODataHelpers.EscapeString(data.Email);
        var appEsc = ODataHelpers.EscapeString(applicationName);
        var users = await _graphServiceClient.Users
            .GetAsync(requestConfiguration =>
            {
                requestConfiguration.QueryParameters.Filter =
                    $"(mail eq '{emailEsc}' or userPrincipalName eq '{emailEsc}' or proxyAddresses/any(c:c eq 'SMTP:{emailEsc}')) and department eq '{appEsc}'";
                requestConfiguration.QueryParameters.Select = new[] { "id", "userPrincipalName", "mail", "displayName", "department" };
                requestConfiguration.QueryParameters.Top = 1;
            }, cancellationToken);

        if (users?.Value == null || users.Value.Count == 0)
            return await CreateErrorResponse(req, "Invalid email or password", correlationId);

        var user = users.Value.First();

        // Validate credentials using password history
        var isValidCredentials = await ValidateUserCredentials(data.Password, applicationName, data.Email, cancellationToken);

        if (isValidCredentials)
        {
            return await CreateJsonResponse(req, new
            {
                success = true,
                message = "Credentials validated successfully"
            }, HttpStatusCode.OK, correlationId);
        }
        else
        {
            return await CreateErrorResponse(req, "Invalid email or password", correlationId);
        }
    }
}
