using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SendGrid;
using SendGrid.Helpers.Mail;
using PasswordHistoryValidator.Shared;

namespace PasswordHistoryValidator.Services
{
    public interface IEmailService
    {
        Task<bool> SendPasswordResetEmailAsync(string toEmail, string resetToken, string verificationCode, string correlationId, CancellationToken cancellationToken = default);
        Task<bool> SendPasswordChangedNotificationAsync(string toEmail, string correlationId, CancellationToken cancellationToken = default);
        Task<bool> SendUserInvitationEmailAsync(string toEmail, string invitationToken, string verificationCode, string applicationName, string firstName, string lastName, string correlationId, CancellationToken cancellationToken = default);
        Task<bool> SendAccountCreatedNotificationAsync(string toEmail, string firstName, string applicationName, string correlationId, CancellationToken cancellationToken = default);
        Task<bool> SendPasswordExpirationNotificationAsync(string toEmail, string applicationName, int daysUntilExpiration, string correlationId, CancellationToken cancellationToken = default);
        Task<bool> SendPasswordExpiredNotificationAsync(string toEmail, string applicationName, string correlationId, CancellationToken cancellationToken = default);
    }

    public class EmailService : IEmailService
    {
        private readonly ILogger<EmailService> _logger;
        private readonly ISendGridClient? _sendGridClient;
        private readonly string _fromEmail;
        private readonly string _resetBaseUrl;
        private readonly string _registrationBaseUrl;
        private readonly string _applicationName;
        private readonly string _fromName;
        private readonly string _passwordResetTemplateId;
        private readonly string _passwordChangedTemplateId;
        private readonly string _userInvitationTemplateId;
        private readonly string _accountCreatedTemplateId;
        private readonly string _passwordExpirationTemplateId;
        private readonly string _passwordExpiredTemplateId;

        public EmailService(ILogger<EmailService> logger, IOptions<SendGridOptions> sendGridOptions, IOptions<PasswordResetOptions> passwordResetOptions, IOptions<AccountRegistrationOptions> accountRegistrationOptions)
        {
            _logger = logger;
            var sendGridOpts = sendGridOptions.Value;
            var passwordResetOpts = passwordResetOptions.Value;
            var accountRegistrationOpts = accountRegistrationOptions.Value;

            // sendgrid client
            if (string.IsNullOrEmpty(sendGridOpts.ApiKey) || sendGridOpts.ApiKey == "REPLACE_WITH_YOUR_SENDGRID_API_KEY")
            {
                _logger.LogError("SendGrid API key not configured. Email operations will fail.");
                _sendGridClient = null;
            }
            else
            {
                _sendGridClient = new SendGridClient(sendGridOpts.ApiKey);
            }

            // config values
            _fromEmail = sendGridOpts.FromEmail ?? throw new InvalidOperationException("SendGrid:FromEmail is required in configuration.");
            _resetBaseUrl = passwordResetOpts.BaseUrl ?? throw new InvalidOperationException("PasswordReset:BaseUrl is required in configuration.");
            _registrationBaseUrl = accountRegistrationOpts.BaseUrl ?? throw new InvalidOperationException("AccountRegistration:BaseUrl is required in configuration.");
            
            _passwordResetTemplateId = sendGridOpts.PasswordResetTemplateId;
            _passwordChangedTemplateId = sendGridOpts.PasswordChangedTemplateId;
            _userInvitationTemplateId = sendGridOpts.UserInvitationTemplateId;
            _accountCreatedTemplateId = sendGridOpts.AccountCreatedTemplateId;
            _passwordExpirationTemplateId = sendGridOpts.PasswordExpirationTemplateId ?? "";
            _passwordExpiredTemplateId = sendGridOpts.PasswordExpiredTemplateId ?? "";
            
            _applicationName = "Password Reset Service";
            _fromName = "Password Reset Service";
        }

        private async Task<bool> SendEmailWithTemplate(string toEmail, string templateId, object templateData, string emailType, string correlationId, CancellationToken cancellationToken = default)
        {
            try
            {
                if (_sendGridClient == null)
                {
                    _logger.LogError("SendGrid client not configured. Cannot send {EmailType} email to {Email} [CorrelationId: {CorrelationId}]", emailType, toEmail, correlationId);
                    return false;
                }

                if (string.IsNullOrEmpty(templateId) || templateId == "d-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx")
                {
                    _logger.LogError("SendGrid template ID not configured for {EmailType} email [CorrelationId: {CorrelationId}]", emailType, correlationId);
                    return false;
                }

                var from = new EmailAddress(_fromEmail, _fromName);
                var to = new EmailAddress(toEmail);

                var message = new SendGridMessage()
                {
                    From = from,
                    TemplateId = templateId
                };
                message.AddTo(to);
                message.SetTemplateData(templateData);
                message.AddCustomArg("X-Correlation-ID", correlationId);
                message.AddCustomArg("X-Email-Type", emailType);
                message.AddCustomArg("X-Application-Name", _applicationName);

                var response = await _sendGridClient.SendEmailAsync(message, cancellationToken);

                if (response.IsSuccessStatusCode)
                {
                    return true;
                }
                else
                {
                    var errorMessage = response.Body != null ? await response.Body.ReadAsStringAsync() : "Unknown error";
                    _logger.LogError("Failed to send {EmailType} email to {Email}. StatusCode: {StatusCode}, Error: {Error} [CorrelationId: {CorrelationId}]",
                        emailType, toEmail, response.StatusCode, errorMessage, correlationId);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending {EmailType} email to {Email} [CorrelationId: {CorrelationId}]", emailType, toEmail, correlationId);
                return false;
            }
        }

        public async Task<bool> SendPasswordResetEmailAsync(string toEmail, string resetToken, string verificationCode, string correlationId, CancellationToken cancellationToken = default)
        {
            var resetLink = $"{_resetBaseUrl}?token={resetToken}";
            var templateData = new
            {
                resetLink = resetLink,
                verificationCode = verificationCode,
                applicationName = _applicationName
            };

            return await SendEmailWithTemplate(toEmail, _passwordResetTemplateId, templateData, "passwordReset", correlationId, cancellationToken);
        }

        public async Task<bool> SendPasswordChangedNotificationAsync(string toEmail, string correlationId, CancellationToken cancellationToken = default)
        {
            var templateData = new
            {
                applicationName = _applicationName,
                email = toEmail,
                changeDate = DateTime.UtcNow.ToString("yyyy-MM-dd"),
                changeTime = DateTime.UtcNow.ToString("HH:mm"),
                correlationId = correlationId
            };

            return await SendEmailWithTemplate(toEmail, _passwordChangedTemplateId, templateData, "passwordChanged", correlationId, cancellationToken);
        }

        public async Task<bool> SendUserInvitationEmailAsync(string toEmail, string invitationToken, string verificationCode, string applicationName, string firstName, string lastName, string correlationId, CancellationToken cancellationToken = default)
        {
            var baseUrl = _registrationBaseUrl.TrimEnd('/');
            var invitationLink = $"{baseUrl}?token={invitationToken}";
            
            var templateData = new
            {
                invitationLink = invitationLink,
                verificationCode = verificationCode,
                applicationName = applicationName,
                firstName = firstName,
                lastName = lastName,
                fullName = $"{firstName} {lastName}"
            };

            return await SendEmailWithTemplate(toEmail, _userInvitationTemplateId, templateData, "invitation", correlationId, cancellationToken);
        }

        public async Task<bool> SendAccountCreatedNotificationAsync(string toEmail, string firstName, string applicationName, string correlationId, CancellationToken cancellationToken = default)
        {
            var templateData = new
            {
                firstName = firstName,
                applicationName = applicationName,
                email = toEmail,
                creationDate = DateTime.UtcNow.ToString("yyyy-MM-dd"),
                creationTime = DateTime.UtcNow.ToString("HH:mm"),
                correlationId = correlationId
            };

            return await SendEmailWithTemplate(toEmail, _accountCreatedTemplateId, templateData, "accountCreated", correlationId, cancellationToken);
        }

        public async Task<bool> SendPasswordExpirationNotificationAsync(string toEmail, string applicationName, int daysUntilExpiration, string correlationId, CancellationToken cancellationToken = default)
        {
            var websiteUrl = _resetBaseUrl.Replace("/Custom-Password-Reset/", "").TrimEnd('/');
            var forgotPasswordUrl = $"{websiteUrl}/Custom-Password-Reset/";

            var templateData = new
            {
                applicationName = applicationName,
                daysUntilExpiration = daysUntilExpiration,
                websiteUrl = websiteUrl,
                forgotPasswordUrl = forgotPasswordUrl,
                email = toEmail,
                notificationDate = DateTime.UtcNow.ToString("yyyy-MM-dd"),
                correlationId = correlationId
            };

            return await SendEmailWithTemplate(toEmail, _passwordExpirationTemplateId, templateData, "passwordExpiration", correlationId, cancellationToken);
        }

        public async Task<bool> SendPasswordExpiredNotificationAsync(string toEmail, string applicationName, string correlationId, CancellationToken cancellationToken = default)
        {
            var websiteUrl = _resetBaseUrl.Replace("/Custom-Password-Reset/", "").TrimEnd('/');
            var forgotPasswordUrl = $"{websiteUrl}/Custom-Password-Reset/";

            var templateData = new
            {
                applicationName = applicationName,
                websiteUrl = websiteUrl,
                forgotPasswordUrl = forgotPasswordUrl,
                email = toEmail,
                expiredDate = DateTime.UtcNow.ToString("yyyy-MM-dd"),
                correlationId = correlationId
            };

            return await SendEmailWithTemplate(toEmail, _passwordExpiredTemplateId, templateData, "passwordExpired", correlationId, cancellationToken);
        }
    }
}
